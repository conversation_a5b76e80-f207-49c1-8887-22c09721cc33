# Corrections System

The corrections system enables collaborative editing and quality control for database content through a structured review process.

## Overview

The correction system consists of three main entities:
- **correction**: The main correction record
- **correction_revision**: Historical revisions of corrections
- **correction_user**: User roles and permissions for corrections

## correction Entity

### Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | `i32` | Primary key, unique correction identifier |
| `status` | `CorrectionStatus` | Current state of the correction |
| `type` | `CorrectionType` | Type of correction being made |
| `entity_type` | `EntityType` | Type of entity being corrected |
| `entity_id` | `i32` | ID of the entity being corrected |
| `created_at` | `DateTimeWithTimeZone` | When the correction was created |
| `handled_at` | `Option<DateTimeWithTimeZone>` | When the correction was processed |

### Correction Status

| Status | Description |
|--------|-------------|
| `Pending` | Awaiting review |
| `Approved` | Accepted and applied |
| `Rejected` | Declined with reason |
| `Superseded` | Replaced by newer correction |

### Correction Types

| Type | Description |
|------|-------------|
| `Create` | Adding new entity |
| `Update` | Modifying existing entity |
| `Delete` | Removing entity |
| `Merge` | Combining duplicate entities |

### Entity Types

Corrections can be applied to various entity types:
- **Artist**: Musicians, circles, groups
- **Release**: Albums, EPs, singles
- **Song**: Individual tracks
- **Event**: Conventions, concerts
- **Tag**: Genre or category tags
- **Label**: Record labels

## correction_revision Entity

### Fields

| Field | Type | Description |
|-------|------|-------------|
| `correction_id` | `i32` | Foreign key to parent correction |
| `entity_history_id` | `i32` | Reference to historical entity state |
| `author_id` | `i32` | User who created this revision |
| `description` | `String` | Explanation of changes made |

### Purpose

Revisions provide:
- **Change tracking**: Complete history of modifications
- **Rollback capability**: Ability to revert changes
- **Attribution**: Clear authorship of changes
- **Documentation**: Explanations for modifications

## correction_user Entity

### Fields

| Field | Type | Description |
|-------|------|-------------|
| `correction_id` | `i32` | Foreign key to correction |
| `user_id` | `i32` | Foreign key to user |
| `user_type` | `CorrectionUserType` | Role of user in correction |

### User Types

| Type | Description |
|------|-------------|
| `Author` | Created the correction |
| `Reviewer` | Assigned to review |
| `Approver` | Has approval authority |
| `Watcher` | Subscribed to updates |

## Workflow Process

### 1. Submission
- User identifies need for correction
- Creates correction with proposed changes
- Provides description and rationale
- System assigns unique ID and sets status to `Pending`

### 2. Review
- Qualified reviewers examine the correction
- May request additional information
- Can approve, reject, or request modifications
- Discussion through comment system

### 3. Decision
- Authorized approver makes final decision
- Approved corrections are applied to database
- Rejected corrections include explanation
- Status updated with timestamp

### 4. Application
- Approved corrections modify target entities
- Historical states preserved in history tables
- Notification sent to relevant users
- Correction marked as `Approved`

## Permission System

### User Roles
- **Contributors**: Can submit corrections
- **Reviewers**: Can review and comment
- **Moderators**: Can approve/reject corrections
- **Administrators**: Full correction management

### Entity-Specific Permissions
- Different entities may have different approval requirements
- Some changes may require multiple approvals
- Sensitive changes may need administrator approval

## Quality Control

### Validation Rules
- Data format validation
- Business rule enforcement
- Duplicate detection
- Consistency checks

### Review Criteria
- Accuracy of information
- Source verification
- Formatting compliance
- Community guidelines adherence

## Database Schema

```sql
-- correction table
CREATE TABLE correction (
    id SERIAL PRIMARY KEY,
    status correction_status NOT NULL DEFAULT 'Pending',
    type correction_type NOT NULL,
    entity_type entity_type NOT NULL,
    entity_id INTEGER NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    handled_at TIMESTAMPTZ
);

-- correction_revision table
CREATE TABLE correction_revision (
    correction_id INTEGER NOT NULL REFERENCES correction(id),
    entity_history_id INTEGER NOT NULL,
    author_id INTEGER NOT NULL REFERENCES user(id),
    description TEXT NOT NULL,
    PRIMARY KEY (correction_id, entity_history_id)
);

-- correction_user table
CREATE TABLE correction_user (
    correction_id INTEGER NOT NULL REFERENCES correction(id),
    user_id INTEGER NOT NULL REFERENCES user(id),
    user_type correction_user_type NOT NULL,
    PRIMARY KEY (correction_id, user_id, user_type)
);
```

## API Endpoints

### Correction Management
- `POST /corrections` - Submit new correction
- `GET /corrections/{id}` - Get correction details
- `PUT /corrections/{id}` - Update correction
- `DELETE /corrections/{id}` - Cancel correction

### Review Operations
- `POST /corrections/{id}/approve` - Approve correction
- `POST /corrections/{id}/reject` - Reject correction
- `GET /corrections/pending` - List pending corrections
- `GET /corrections/user/{id}` - User's corrections

## Monitoring and Analytics

### Metrics
- Correction submission rates
- Review processing times
- Approval/rejection ratios
- User contribution statistics

### Quality Indicators
- Error rates in approved corrections
- Community feedback on changes
- Reversion frequency
- User satisfaction scores

## Best Practices

### For Contributors
- Provide clear, detailed descriptions
- Include sources when possible
- Follow formatting guidelines
- Respond promptly to reviewer feedback

### For Reviewers
- Review thoroughly but efficiently
- Provide constructive feedback
- Maintain consistency in decisions
- Document reasoning for rejections

### For System Design
- Implement comprehensive audit trails
- Provide clear user interfaces
- Automate routine validations
- Enable efficient batch operations
